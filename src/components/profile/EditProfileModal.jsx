import { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';
import toast from 'react-hot-toast';
import { changeUserInfo, deleteAccount, uploadAvatarURL } from '@api/users';
import useAuth from '@hooks/useAuth';
import CryptoJS from 'crypto-js';

// Function to calculate MD5 hash of a File object (returns base64 encoded)
const calculateMD5 = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const wordArray = CryptoJS.lib.WordArray.create(event.target.result);
        const md5Hash = CryptoJS.MD5(wordArray).toString(CryptoJS.enc.Base64);
        resolve(md5Hash);
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
};

const EditProfileModal = ({ isOpen, onClose, user }) => {
  const { updateUser, logout } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [nickname, setNickname] = useState(user?.nickname || '');
  const [avatarFile, setAvatarFile] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState(user?.avatar || '');
  const [isDeleting, setIsDeleting] = useState(false);

  // Reset form state when modal opens or user changes
  useEffect(() => {
    if (isOpen && user) {
      setNickname(user.nickname || '');
      setAvatarPreview(user.avatar || '');
      setAvatarFile(null);
      setIsLoading(false);
      setIsDeleting(false);
    }
  }, [isOpen, user]);

  // Reset form state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setAvatarFile(null);
      setIsLoading(false);
      setIsDeleting(false);
      // Reset preview to user's actual avatar when modal closes
      if (user) {
        setAvatarPreview(user.avatar || '');
        setNickname(user.nickname || '');
      }
    }
  }, [isOpen, user]);

  // Custom close handler to ensure state is reset
  const handleClose = () => {
    // Reset to original user data
    if (user) {
      setNickname(user.nickname || '');
      setAvatarPreview(user.avatar || '');
    }
    setAvatarFile(null);
    setIsLoading(false);
    setIsDeleting(false);
    onClose();
  };

  const handleAvatarChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setAvatarFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const nicknameChanged = nickname !== user?.nickname;
      let finalAvatarValue = user?.avatar; // Start with current avatar

      if (avatarFile) {
        // A new file is selected, upload it
        // 1. 获取预签名 URL
        const content_md5 = await calculateMD5(avatarFile); // Calculate MD5
        const uploadUrlData = await uploadAvatarURL({
          filename: avatarFile.name,
          content_type: avatarFile.type,
          content_size: avatarFile.size,
          content_md5: content_md5, // Include MD5
        });

        // 2. 上传文件到 AWS 使用原始文件数据 (不使用 FormData)
        console.log('Upload details:', {
          url: uploadUrlData.data.url,
          method: uploadUrlData.data.method || 'PUT',
          fileType: avatarFile.type,
          fileSize: avatarFile.size,
          contentMD5: content_md5
        });

        const uploadResponse = await fetch(uploadUrlData.data.url, {
          method: uploadUrlData.data.method || 'PUT',
          body: avatarFile,
          headers: {
            'Content-Type': avatarFile.type,
            'Content-MD5': content_md5,
            'Content-Length': avatarFile.size.toString(),
          },
        });

        if (!uploadResponse.ok) {
          throw new Error(`Avatar upload failed: ${uploadResponse.status} - ${uploadResponse.statusText}`);
        }

        // 3. 使用 url 作为头像值
        finalAvatarValue = uploadUrlData.data.url;
        console.log('Avatar upload successful:', uploadUrlData);
      }

      const avatarValueChanged = finalAvatarValue !== user?.avatar;

      if (nicknameChanged || avatarValueChanged) {
        const updateData = {};
        updateData.nickname = nickname;
        updateData.avatar = finalAvatarValue;
        

        await changeUserInfo(updateData);
        await updateUser();
        toast.success('个人资料已更新');
        handleClose();
      } else {
        // No changes, just close the modal
        toast('没有检测到资料修改');
        handleClose();
      }
    } catch (error) {
      console.error('更新个人资料失败：', error);
      toast.error('更新个人资料失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (window.confirm('确定要删除账户吗？此操作无法撤销。')) {
      setIsDeleting(true);
      try {
        await deleteAccount();
        toast.success('账户已删除');
        logout();
      } catch (error) {
        console.error('删除账户失败：', error);
        toast.error('删除账户失败');
      } finally {
        setIsDeleting(false);
      }
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={handleClose}
      className="fixed inset-0 z-50 overflow-y-auto"
    >
      <div className="flex min-h-screen items-center justify-center p-4">
        <Dialog.Overlay className="fixed inset-0 bg-black/70" />

        <div className="glass-dark relative rounded-xl p-6 w-full max-w-md">
          <Dialog.Title className="text-xl font-bold mb-6">
            编辑个人资料
          </Dialog.Title>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                头像
              </label>
              <div className="flex items-center space-x-4">
                <div className="relative">
                  {avatarPreview ? (
                    <img
                      src={avatarPreview}
                      alt="Avatar preview"
                      className="w-20 h-20 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-20 h-20 rounded-full bg-primary-600 flex items-center justify-center text-2xl font-bold">
                      {user?.nickname?.charAt(0)?.toUpperCase() || 'U'}
                    </div>
                  )}
                  <label
                    className="absolute bottom-0 right-0 bg-primary-600 p-1.5 rounded-full cursor-pointer hover:bg-primary-500 transition-colors"
                    htmlFor="avatar-input"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                  </label>
                </div>
                <input
                  type="file"
                  id="avatar-input"
                  className="hidden"
                  accept="image/*"
                  onChange={handleAvatarChange}
                />
                <div className="text-sm text-gray-400">
                  点击图标上传新头像
                </div>
              </div>
            </div>

            <div>
              <label
                htmlFor="nickname"
                className="block text-sm font-medium text-gray-300 mb-2"
              >
                昵称
              </label>
              <input
                type="text"
                id="nickname"
                value={nickname}
                onChange={(e) => setNickname(e.target.value)}
                className="glass w-full px-4 py-2 rounded-lg focus:ring-2 focus:ring-primary-500 focus:outline-none"
                placeholder="输入你的昵称"
              />
            </div>

            <div className="flex justify-between pt-4">
              <button
                type="button"
                onClick={handleDeleteAccount}
                disabled={isDeleting}
                className="text-red-500 hover:text-red-400 transition-colors text-sm"
              >
                {isDeleting ? '正在删除...' : '删除账户'}
              </button>
              <div className="space-x-3">
                <button
                  type="button"
                  onClick={handleClose}
                  className="btn-glass"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="btn-primary"
                >
                  {isLoading ? '保存中...' : '保存'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </Dialog>
  );
};

export default EditProfileModal;